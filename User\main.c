#include "stm32f10x.h"                  // Device header
#include "Delay.h"

#include "Serial.h"
#include "ADC.h"
#include "Serial.h"
#include <string.h> 
#include <stdio.h>
#define USART1_RX_BUFFER_SIZE 64
char usart1_rx_line_buffer[USART1_RX_BUFFER_SIZE];
uint8_t usart1_rx_line_idx = 0;
volatile uint8_t usart1_line_received_flag = 0;
float temp_D_float_val=0.0f;

uint8_t temp_X;
uint8_t parsed_D_value;
float parsed_D_value_float;
int parsed_X_value = 0;

uint8_t RxData;			//定义用于接收串口数据的变量
uint8_t x;
float I;
float P;
uint8_t D;
uint16_t ADValue;			//定义AD值变量
float Voltage;
float A;
char debug_serial1_buf[100];
void HandleUsart1RxByte(uint8_t rx_byte)
{
    // 确保缓冲区不会溢出
    if (usart1_rx_line_idx < USART1_RX_BUFFER_SIZE - 1) // 留一个位置给字符串结束符 '\0'
    {
        if (rx_byte == '\n' || rx_byte == '\r') // 检测到行结束符 (换行符或回车符)
        {
            if (usart1_rx_line_idx > 0) // 确保在行结束符之前有实际数据
            {
                usart1_rx_line_buffer[usart1_rx_line_idx] = '\0'; // 字符串以 null 结尾
                usart1_line_received_flag = 1;                   // 设置标志，通知主循环有完整行数据待处理
            }
            usart1_rx_line_idx = 0; // 重置索引，准备接收下一行
        }
        else
        {
            usart1_rx_line_buffer[usart1_rx_line_idx++] = rx_byte; // 将接收到的字节存入缓冲区
        }
    }
    else // 缓冲区已满，重置以防止溢出，并丢弃当前行（可选：可以添加错误处理）
    {
        usart1_rx_line_idx = 0;
        Serial_SendString("USART1 RX Buffer Overflow!\r\n"); // 调试信息
    }
}

int main(void)
{	
	Serial_SendString("STM32 Started!\r\n");
	Serial_SendString("Waiting for Camera Data on USART1...\r\n");
	/*串口初始化*/
	Serial_Init();//串口初始化
	Serial2_Init();				
	AD_Init();			//ADC初始化
	char String[100];
	
	while (1)
	{
		
//		Serial_SendString("STM32 Started!\r\n");
//		Serial_SendString("Waiting for Camera Data on USART1...\r\n");
		
		
//		ADValue = AD_GetValue();					//获取AD转换的值
//		Voltage = (float)ADValue / 4095 * 3.3;
//		A = Voltage*10/0.5;   //计算电流
//		
//		if(Serial_GetRxFlag() == 1) // 检查USART1是否接收到新字节
//        {
//            uint8_t rx_byte = Serial_GetRxData(); // 获取接收到的字节
//            HandleUsart1RxByte(rx_byte);          // 将字节传递给行处理函数
//        }

//        // ------------------ 2. 处理接收到的完整数据行 ------------------
//        if (usart1_line_received_flag == 1) // 如果收到了一行完整的数据
//        {
//            usart1_line_received_flag = 0; // 清除标志位，表示已处理
//			Serial_SendString("RX: "); // 标识符
//            Serial_SendString(usart1_rx_line_buffer); // 打印整个接收缓冲区内容
//            Serial_SendString("\r\n"); // 换行
//			if (usart1_rx_line_idx > 0 && usart1_rx_line_buffer[0] == '@')
//            {
//               
//				if(sscanf(&usart1_rx_line_buffer[1],"%f",&temp_D_float_val)==1)
//				{
//					parsed_D_value_float=temp_D_float_val;
//					parsed_D_value = (int)temp_D_float_val;
//									
//					sprintf(debug_serial1_buf, "Parsed D (float): %.2f, (int): %d\r\n", parsed_D_value_float, parsed_D_value);
//					Serial_SendString(debug_serial1_buf);
//					sprintf(String,"index.n2.val=%d\xff\xff\xff",parsed_D_value);
//					Serial2_SendString(String);
//					Delay_ms(5);
//					
//					
//					sprintf(String,"index.x0.val=%f\xff\xff\xff",I*10);
//					Serial2_SendString(String);
//					Delay_ms(5);
//					sprintf(String,"index.x1.val=%f\xff\xff\xff",P*10);
//					Serial2_SendString(String);
//					Delay_ms(5);
//					sprintf(String,"index.n2.val=%d\xff\xff\xff",D);
//					Serial2_SendString(String);
//					Delay_ms(5);
//					sprintf(String,"index.x2.val=%d\xff\xff\xff",x);
//					Serial2_SendString(String);
//					Delay_ms(5);
//				}
//				
//		
//	}
//}
}
	}